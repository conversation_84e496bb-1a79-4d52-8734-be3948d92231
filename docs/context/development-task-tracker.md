# PatternTrade API Development Task Tracker

This document tracks upcoming development tasks for the PatternTrade API project. Use this as a persistent reference for planning, prioritization, and context switching.

---

## August 10, 2025
- [x] Add Zerodha Kite login. remove public endpoint for broker login.
- [ ] Make the datastore module work with gRPC and symbol module.
- [ ] Integrate scanner module with Chartink scanner.
- [x] Utilise cls module
- [ ] Add nestjs-bullmq
- [x] get userid from cls instead of dto object
- [ ] make kiteconnection instance a singleton


---

_Last updated: 10 August 2025_